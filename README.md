# 数据可视化期末项目 - 全球电影票房数据分析

## 项目概述
本项目围绕全球电影票房数据进行数据采集、预处理和可视化分析，旨在发现电影行业的趋势和规律。

## 项目结构
```
数据可视化期末/
├── data/                   # 数据文件夹
│   ├── raw/               # 原始数据
│   └── processed/         # 处理后的数据
├── src/                   # 源代码文件夹
│   ├── data_collection.py # 数据采集脚本
│   ├── data_preprocessing.py # 数据预处理脚本
│   └── visualization/     # 可视化脚本
├── notebooks/             # Jupyter笔记本
├── reports/               # 报告和图表
└── requirements.txt       # 依赖包列表
```

## 环境配置
1. 安装Python依赖包：
```bash
pip install -r requirements.txt
```

## 数据来源
- 豆瓣电影 Top250
- IMDB电影数据
- 其他电影数据网站

## 可视化类型
本项目将创建以下8种类型的图表：
1. 类别数据 - 电影类型分布
2. 数值数据 - 票房与评分关系
3. 层次关系数据 - 票房排名
4. 局部整体数据 - 类型占比
5. 数据分布 - 评分分布
6. 时间序列数据 - 年度趋势
7. 多维数据 - 多因素分析
8. 网络关系数据 - 导演-电影关系

## 使用的可视化库
- matplotlib
- seaborn  
- pyecharts
- plotly
